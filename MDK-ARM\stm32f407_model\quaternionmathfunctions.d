stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/QuaternionMathFunctions.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_norm_f32.c
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/quaternion_math_functions.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h
stm32f407_model\quaternionmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\ARMCC\Bin\..\include\stdint.h
stm32f407_model\quaternionmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\ARMCC\Bin\..\include\string.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\ARMCC\Bin\..\include\math.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\ARMCC\Bin\..\include\float.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\ARMCC\Bin\..\include\limits.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/none.h
stm32f407_model\quaternionmathfunctions.o: E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/utils.h
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_inverse_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_conjugate_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_normalize_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_product_single_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_product_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion2rotation_f32.c
stm32f407_model\quaternionmathfunctions.o: E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_rotation2quaternion_f32.c
