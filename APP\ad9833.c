#include "ad9833.h"                                                                    // 包含AD9833驱动头文件

#define fre_adjust 1.073741824   //频率校准系数

/**************************************
*   �� �� ��: ad9833_init
*   ����˵��: ad9833��ʼ��
*   ��    ��: ��
*   �� �� ֵ: ��
*************************************/
// 当前配置状态 - 内部变量
static AD9833_ConfigTypeDef current_config = {0};                                     // 保存当前AD9833的配置状态，初始化为0

/**
 * @brief AD9833初始化函数 - 增强版本
 * @param 无
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};                                            // 定义GPIO初始化结构体并清零

    // 使能GPIOA时钟 - HAL库方式
    __HAL_RCC_GPIOA_CLK_ENABLE();                                                      // 使能GPIOA时钟，为GPIO配置做准备

    // 配置GPIO引脚为推挽输出模式
    GPIO_InitStruct.Pin = AD9833_SDATA_PIN | AD9833_SCLK_PIN | AD9833_FSYNC_PIN;      // 选择要配置的引脚：数据、时钟、同步信号
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;     // 推挽输出                       // 设置为推挽输出模式，提供强驱动能力
    GPIO_InitStruct.Pull = GPIO_NOPULL;             // 无上下拉                       // 不使用内部上下拉电阻
    GPIO_InitStruct.Speed = AD9833_GPIO_SPEED;      // 使用配置的速度                  // 设置GPIO翻转速度，影响信号质量
    HAL_GPIO_Init(AD9833_GPIO_PORT, &GPIO_InitStruct);                                // 应用GPIO配置到硬件

    // 初始化GPIO引脚为高电平
    HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_SDATA_PIN | AD9833_SCLK_PIN | AD9833_FSYNC_PIN, GPIO_PIN_SET); // 设置所有控制引脚为高电平，确保SPI空闲状态

    // 复位AD9833并设置默认配置
    AD9833_WriteData(0x0100); // 复位AD9833                                          // 发送复位命令，将AD9833恢复到初始状态

    // 初始化当前配置为默认值
    current_config.freq_reg = AD9833_REG_FREQ0;                                       // 默认使用频率寄存器0
    current_config.frequency = AD9833_DEFAULT_FREQ;                                   // 设置默认频率值
    current_config.phase_reg = AD9833_REG_PHASE0;                                     // 默认使用相位寄存器0
    current_config.phase = AD9833_DEFAULT_PHASE;                                      // 设置默认相位值
    current_config.waveform = AD9833_DEFAULT_WAVE;                                    // 设置默认波形类型

    return AD9833_OK;                                                                  // 返回初始化成功状态
}

/**************************************
*   �� �� ��: AD9833_Delay
*   ����˵��: ad9833�ӳ�
*   ��    ��: ��
*   �� �� ֵ: ��
*************************************/
/**
 * @brief AD9833时序延时函数
 * @param 无
 * @retval 无
 */
static void AD9833_Delay(void)
{
    uint16_t i;                                                                        // 定义循环计数器
    for (i = 0; i < AD9833_DELAY_CYCLES; i++);                                        // 执行空循环延时，确保SPI时序满足AD9833要求
}
/**************************************
*   �� �� ��: ad9833_write_data
*   ����˵��: ad9833д��16λ����
*   ��    ��: txdata����д���16λ����
*   �� �� ֵ: ��
*************************************/
void AD9833_WriteData(uint16_t txdata)
{
    int i;                                                                             // 定义位计数器
    AD9833_SCLK_HIGH();                                                                // 设置时钟为高电平，准备SPI传输
    AD9833_FSYNC_HIGH();                                                               // 设置同步信号为高电平
    AD9833_FSYNC_LOW();                                                                // 拉低同步信号，开始数据传输
    //写16位数据
    for(i=0;i<16;i++)                                                                  // 循环发送16位数据，从MSB开始
    {

        if (txdata & 0x8000)                                                           // 检查当前最高位是否为1
            AD9833_SDATA_HIGH();                                                       // 如果是1，设置数据线为高电平
        else
            AD9833_SDATA_LOW();                                                        // 如果是0，设置数据线为低电平
        AD9833_Delay();                                                                // 延时确保数据建立时间
        AD9833_SCLK_LOW();                                                             // 拉低时钟，产生下降沿锁存数据
        AD9833_Delay();                                                                // 延时确保时钟低电平保持时间
        AD9833_SCLK_HIGH();                                                            // 拉高时钟，准备下一位数据
        txdata<<=1;                                                                    // 左移数据，准备发送下一位
    }
    AD9833_FSYNC_HIGH();                                                               // 拉高同步信号，结束数据传输
}

/**************************************
*   �� �� ��: AD9833_SetFrequency
*   ����˵��: ad9833����Ƶ�ʼĴ���
*   ��    ��: reg����д���Ƶ�ʼĴ���   
              fout��Ƶ��ֵ
*   �� �� ֵ: ��
*************************************/
/**
 * @brief AD9833设置频率函数 - 增强版本
 * @param reg 频率寄存器选择
 * @param fout 频率值 (Hz)
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_SetFrequency(uint16_t reg, double fout)
{
#if AD9833_ENABLE_FREQ_VALIDATION
    // 频率范围验证
    if (fout < AD9833_MIN_FREQ_HZ || fout > AD9833_MAX_FREQ_HZ) {                      // 检查频率是否在有效范围内
        return AD9833_ERROR_FREQ_OUT_OF_RANGE;                                        // 返回频率超出范围错误
    }

    // 寄存器验证
    if (reg != AD9833_REG_FREQ0 && reg != AD9833_REG_FREQ1) {                         // 检查寄存器地址是否有效
        return AD9833_ERROR_INVALID_REGISTER;                                         // 返回无效寄存器错误
    }
#endif

    uint32_t frequence_LSB, frequence_MSB;                                            // 定义频率数据的低位和高位
    uint32_t frequence_hex;                                                            // 定义频率的十六进制表示

    // 使用配置的频率计算参数
    frequence_hex = (uint32_t)((fout * AD9833_FREQ_SCALE) + 0.5); // 四舍五入        // 将频率转换为AD9833内部表示，使用四舍五入提高精度

    // 分离为两个14位值
    frequence_LSB = frequence_hex & 0x3FFF;         // 低14位                        // 提取频率数据的低14位
    frequence_MSB = (frequence_hex >> 14) & 0x3FFF; // 高14位                       // 提取频率数据的高14位

    // 添加寄存器选择位
    frequence_LSB |= reg;                                                              // 在低位数据中添加寄存器选择位
    frequence_MSB |= reg;                                                              // 在高位数据中添加寄存器选择位

    // 写入频率数据
    AD9833_WriteData(0x2100);           // 选择数据一次写入，B28位和RESET位为1        // 设置控制寄存器，允许28位频率数据写入
    AD9833_WriteData(frequence_LSB);    // 写入低14位                               // 先写入频率数据的低14位
    AD9833_WriteData(frequence_MSB);    // 写入高14位                               // 再写入频率数据的高14位

    // 更新当前配置
    if (reg == AD9833_REG_FREQ0) {                                                     // 如果设置的是频率寄存器0
        current_config.freq_reg = AD9833_REG_FREQ0;                                   // 更新当前使用的频率寄存器
        current_config.frequency = fout;                                              // 更新当前频率值
    }

    return AD9833_OK;                                                                  // 返回操作成功状态
}

/**************************************
*   �� �� ��: ad9833_write_data
*   ����˵��: ad9833������λ�Ĵ���
*   ��    ��: reg����д�����λ�Ĵ���   
              fout����λֵ
*   �� �� ֵ: ��
*************************************/
/**
 * @brief AD9833设置相位函数 - 增强版本
 * @param reg 相位寄存器选择
 * @param val 相位值 (0-4095)
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_SetPhase(uint16_t reg, uint16_t val)
{
#if AD9833_ENABLE_PHASE_VALIDATION
    // 相位范围验证
    if (val > AD9833_MAX_PHASE) {                                                      // 检查相位值是否超出12位范围(0-4095)
        return AD9833_ERROR_PHASE_OUT_OF_RANGE;                                       // 返回相位超出范围错误
    }

    // 寄存器验证
    if (reg != AD9833_REG_PHASE0 && reg != AD9833_REG_PHASE1) {                       // 检查相位寄存器地址是否有效
        return AD9833_ERROR_INVALID_REGISTER;                                         // 返回无效寄存器错误
    }
#endif

    uint16_t phase = reg | (val & 0x0FFF); // 确保只使用12位相位数据                   // 组合寄存器地址和相位数据，确保相位数据只占用12位
    AD9833_WriteData(phase);                                                           // 将相位数据写入AD9833

    // 更新当前配置
    if (reg == AD9833_REG_PHASE0) {                                                    // 如果设置的是相位寄存器0
        current_config.phase_reg = AD9833_REG_PHASE0;                                 // 更新当前使用的相位寄存器
        current_config.phase = val;                                                   // 更新当前相位值
    }

    return AD9833_OK;                                                                  // 返回操作成功状态
}

/**************************************
*   �� �� ��: AD9833_SetWave
*   ����˵��: ad9833���ò���
*   ��    ��: WaveMode������������� 
              Freq_SFR�������Ƶ�ʼĴ�������
              Phase_SFR���������λ�Ĵ�������
*   �� �� ֵ: ��
*************************************/
/**
 * @brief AD9833设置波形函数 - 增强版本
 * @param WaveMode 波形类型
 * @param Freq_SFR 频率寄存器选择
 * @param Phase_SFR 相位寄存器选择
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_SetWave(uint32_t WaveMode, uint32_t Freq_SFR, uint32_t Phase_SFR)
{
#if AD9833_ENABLE_ERROR_HANDLING
    // 波形类型验证
    if (WaveMode != AD9833_OUT_SINUS && WaveMode != AD9833_OUT_TRIANGLE &&           // 检查波形类型是否为支持的类型
        WaveMode != AD9833_OUT_MSB && WaveMode != AD9833_OUT_MSB2) {                 // 支持正弦波、三角波、方波等模式
        return AD9833_ERROR_INVALID_WAVEFORM;                                         // 返回无效波形类型错误
    }
#endif

    uint32_t val = (WaveMode | Freq_SFR | Phase_SFR);                                 // 组合波形模式、频率选择和相位选择位
    AD9833_WriteData(val);                                                             // 将控制字写入AD9833

    // 更新当前配置
    current_config.waveform = WaveMode;                                                // 更新当前波形类型

    return AD9833_OK;                                                                  // 返回操作成功状态
}

/**
 * @brief AD9833完整配置函数 - 增强版本
 * @param Freq_SFR 频率寄存器选择
 * @param Freq 频率值
 * @param Phase_SFR 相位寄存器选择
 * @param Phase 相位值
 * @param WaveMode 波形类型
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_Setup(uint32_t Freq_SFR, double Freq, uint32_t Phase_SFR, uint32_t Phase, uint32_t WaveMode)
{
    AD9833_StatusTypeDef status;                                                       // 定义状态变量用于错误检查
    uint32_t Fsel, Psel;                                                               // 定义频率和相位选择位变量

    // 复位AD9833
    AD9833_WriteData(0x0100);                                                          // 发送复位命令，设置RESET位
    AD9833_WriteData(0x2100);                                                          // 清除RESET位，设置B28位允许28位频率写入

    // 设置频率
	status = AD9833_SetFrequency(Freq_SFR, Freq*fre_adjust);                                     // 调用频率设置函数//fre_adjust为校准系数
    if (status != AD9833_OK) return status;                                           // 如果频率设置失败，立即返回错误状态

    // 设置相位
    status = AD9833_SetPhase(Phase_SFR, Phase);                                       // 调用相位设置函数
    if (status != AD9833_OK) return status;                                           // 如果相位设置失败，立即返回错误状态

    // 确定频率和相位选择位
    Fsel = (Freq_SFR == AD9833_REG_FREQ0) ? AD9833_FSEL0 : AD9833_FSEL1;              // 根据频率寄存器选择对应的选择位
    Psel = (Phase_SFR == AD9833_REG_PHASE0) ? AD9833_PSEL0 : AD9833_PSEL1;            // 根据相位寄存器选择对应的选择位

    // 设置波形
    status = AD9833_SetWave(WaveMode, Fsel, Psel);                                    // 调用波形设置函数，组合所有参数

    return status;                                                                     // 返回最终操作状态
}

/**
 * @brief 从配置结构体配置AD9833
 * @param config 配置结构体指针
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_ConfigureFromStruct(const AD9833_ConfigTypeDef* config)
{
    if (config == NULL) {                                                              // 检查配置结构体指针是否为空
        return AD9833_ERROR_INVALID_REGISTER; // 复用错误码                           // 返回错误状态（复用寄存器错误码）
    }

    return AD9833_Setup(config->freq_reg, config->frequency,                          // 使用结构体中的参数调用完整配置函数
                       config->phase_reg, config->phase, config->waveform);           // 传递频率寄存器、频率值、相位寄存器、相位值、波形类型
}

/**
 * @brief 设置默认配置
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_SetDefaultConfig(void)
{
    return AD9833_Setup(AD9833_REG_FREQ0, AD9833_DEFAULT_FREQ,                        // 使用预定义的默认参数配置AD9833
                       AD9833_REG_PHASE0, AD9833_DEFAULT_PHASE, AD9833_DEFAULT_WAVE); // 频率寄存器0、默认频率、相位寄存器0、默认相位、默认波形
}

/**
 * @brief 复位AD9833
 * @retval AD9833_StatusTypeDef 操作状态
 */
AD9833_StatusTypeDef AD9833_Reset(void)
{
    AD9833_WriteData(0x0100); // 设置RESET位                                         // 发送复位命令，将AD9833复位到初始状态
    AD9833_WriteData(0x0000); // 清除RESET位                                         // 清除复位位，允许正常操作
    return AD9833_OK;                                                                  // 返回操作成功状态
}

/**
 * @brief 获取当前配置
 * @param config 配置结构体指针
 * @retval 无
 */
void AD9833_GetConfig(AD9833_ConfigTypeDef* config)
{
    if (config != NULL) {                                                              // 检查配置结构体指针是否有效
        *config = current_config;                                                      // 将当前配置复制到用户提供的结构体中
    }
}

//=============================================================================
// 兼容性函数 - 保持原有接口
//=============================================================================

/**
 * @brief 兼容旧版频率设置函数
 */
void AD9833_SetFrequency_Legacy(uint16_t reg, double fout)
{
    AD9833_SetFrequency(reg, fout);                                                    // 调用新版频率设置函数，忽略返回值保持兼容性
}

/**
 * @brief 兼容旧版相位设置函数
 */
void AD9833_SetPhase_Legacy(uint16_t reg, uint16_t val)
{
    AD9833_SetPhase(reg, val);                                                         // 调用新版相位设置函数，忽略返回值保持兼容性
}

/**
 * @brief 兼容旧版完整配置函数
 */
void AD9833_Setup_Legacy(uint32_t Freq_SFR, double Freq, uint32_t Phase_SFR, uint32_t Phase, uint32_t WaveMode)
{
    AD9833_Setup(Freq_SFR, Freq, Phase_SFR, Phase, WaveMode);                         // 调用新版完整配置函数，忽略返回值保持兼容性
}


