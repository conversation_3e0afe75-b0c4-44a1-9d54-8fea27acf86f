# DAC直流电压输出控制说明

## 功能概述
本项目实现了通过按键控制DAC输出直流电压的功能，支持0-3.3V范围内的11级电压输出。

## 🔌 硬件连接

### **示波器连接方法**
```
STM32F407开发板:
PA4 (DAC_OUT1) -----> 示波器探头正极 (信号线)
GND              -----> 示波器探头负极 (地线)
```

### **关键连接检查**
1. **PA4引脚位置**: LQFP144封装第20脚
2. **电源连接**:
   - VDD = 3.3V (数字电源)
   - VDDA = 3.3V (模拟电源) ⚠️ **必须连接**
   - VREF+ = 3.3V (DAC参考电压) ⚠️ **必须连接**
3. **地线连接**: VSS/VSSA接GND

### **示波器设置**
- 电压范围: 0-5V
- 时间基准: DC耦合
- 触发方式: 自动
- 输入阻抗: 1MΩ (高阻抗)

## 硬件配置

### CubeMX配置要点

#### 1. DAC配置
- **外设**: DAC
- **通道**: DAC_OUT1 (PA4引脚)
- **触发方式**: Software trigger (软件触发)
- **输出缓冲**: Enabled (启用)
- **波形生成**: Disabled (禁用，用于直流输出)

#### 2. GPIO配置
- **PA4**: DAC_OUT1 (模拟输出)
- **PE2**: KEY2 (上拉输入) - 减少电压等级
- **PE3**: KEY1 (上拉输入) - 增加电压等级  
- **PE4**: KEY0 (上拉输入) - 频率控制
- **PA0**: KEY_UP (无上拉输入) - 波形切换

#### 3. 时钟配置
- 确保DAC时钟已启用
- 系统时钟配置为168MHz

## 软件实现

### 按键功能分配
- **KEY2 (PE2)**: 减少DAC电压等级
- **KEY1 (PE3)**: 增加DAC电压等级
- **KEY0 (PE4)**: AD9833频率控制
- **KEY_UP (PA0)**: AD9833波形切换

### 电压等级
- 等级范围: 0-33 (共34级)
- 电压范围: 0V - 3.3V
- 每级电压: 0.1V (精确步进)
- DAC分辨率: 12位 (0-4095)

### 核心函数

#### `dac_voltage_control_init()`
初始化DAC电压控制，设置初始电压为0V。

#### `key_task()`
按键扫描和处理函数，包含DAC电压控制逻辑。

## 使用方法

1. **编译和下载程序**到STM32F407开发板
2. **连接万用表**到PA4引脚测量输出电压
3. **按键操作**:
   - 按KEY1增加电压等级
   - 按KEY2减少电压等级
   - 通过串口查看调试信息

## 调试信息
程序会通过UART1输出调试信息，包括：
- 当前电压等级
- DAC数字值
- 对应的模拟电压值

## 🔧 故障排除

### **串口有输出但示波器无信号**

#### **1. 硬件检查清单**
- ✅ PA4引脚是否正确连接到示波器
- ✅ 示波器地线是否连接到开发板GND
- ✅ VDDA (模拟电源) 是否连接到3.3V
- ✅ VREF+ (参考电压) 是否连接到3.3V
- ✅ 示波器设置为DC耦合模式

#### **2. 软件检查**
```c
// 在main函数中添加硬件测试
dac_hardware_test(); // 测试DAC输出
```

#### **3. 万用表验证**
- 用万用表DC档测量PA4引脚电压
- 应该能测到0V-3.3V变化

### 常见问题

1. **DAC完全无输出**
   - 检查VDDA是否连接 ⚠️ **最常见原因**
   - 确认PA4没有被其他功能占用
   - 验证DAC时钟是否启用

2. **电压范围不对**
   - VREF+必须连接到3.3V
   - 检查负载阻抗 (建议>10kΩ)
   - 验证示波器输入阻抗设置

3. **按键无响应**
   - 检查GPIO上拉电阻配置
   - 验证按键硬件连接
   - 确认key_task()在主循环中调用

## 技术参数
- **输出电压范围**: 0V - 3.3V
- **分辨率**: 12位 (4096级)
- **用户控制级数**: 34级 (0-33)
- **电压步进**: 0.1V (精确控制)
- **输出阻抗**: 约15kΩ (缓冲启用时)
- **建议负载**: >10kΩ

## 扩展功能
可以通过修改代码实现：
- 更多电压等级
- 精确电压设置
- 电压斜坡输出
- 多通道DAC控制
