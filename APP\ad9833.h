#ifndef _AD9833_H                                                                      // 防止头文件重复包含
#define _AD9833_H                                                                      // 定义头文件标识符
#include "main.h"                                                                      // 包含主头文件，获取HAL库定义
#include "ad9833_config.h"                                                             // 包含AD9833配置文件

//AD9833 GPIO操作宏 - HAL库兼容
#define AD9833_SDATA_HIGH() HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_SDATA_PIN, GPIO_PIN_SET)    // 设置SDATA引脚为高电平
#define AD9833_SDATA_LOW()  HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_SDATA_PIN, GPIO_PIN_RESET)  // 设置SDATA引脚为低电平
#define AD9833_SCLK_HIGH()  HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_SCLK_PIN, GPIO_PIN_SET)     // 设置SCLK引脚为高电平
#define AD9833_SCLK_LOW()   HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_SCLK_PIN, GPIO_PIN_RESET)   // 设置SCLK引脚为低电平
#define AD9833_FSYNC_HIGH() HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_FSYNC_PIN, GPIO_PIN_SET)    // 设置FSYNC引脚为高电平
#define AD9833_FSYNC_LOW()  HAL_GPIO_WritePin(AD9833_GPIO_PORT, AD9833_FSYNC_PIN, GPIO_PIN_RESET)  // 设置FSYNC引脚为低电平

//兼容原有的简化操作宏
#define AD9833_SDATA  (HAL_GPIO_ReadPin(AD9833_GPIO_PORT, AD9833_SDATA_PIN) ? 1 : 0)   // 读取SDATA引脚状态，返回1或0
#define AD9833_SCLK   (HAL_GPIO_ReadPin(AD9833_GPIO_PORT, AD9833_SCLK_PIN) ? 1 : 0)    // 读取SCLK引脚状态，返回1或0
#define AD9833_FSYNC  (HAL_GPIO_ReadPin(AD9833_GPIO_PORT, AD9833_FSYNC_PIN) ? 1 : 0)   // 读取FSYNC引脚状态，返回1或0
/* WaveMode - 波形模式定义 */
#define AD9833_OUT_SINUS    ((0 << 5) | (0 << 1) | (0 << 3))                          // 正弦波输出模式
#define AD9833_OUT_TRIANGLE ((0 << 5) | (1 << 1) | (0 << 3))                          // 三角波输出模式
#define AD9833_OUT_MSB      ((1 << 5) | (0 << 1) | (1 << 3))                          // 方波输出模式(MSB)
#define AD9833_OUT_MSB2     ((1 << 5) | (0 << 1) | (0 << 3))                          // 方波输出模式(MSB/2)
/* Registers - 寄存器地址定义 */
#define AD9833_REG_CMD      (0 << 14)                                                  // 控制寄存器地址
#define AD9833_REG_FREQ0    (1 << 14)                                                  // 频率寄存器0地址
#define AD9833_REG_FREQ1    (2 << 14)                                                  // 频率寄存器1地址
#define AD9833_REG_PHASE0   (6 << 13)                                                  // 相位寄存器0地址
#define AD9833_REG_PHASE1   (7 << 13)                                                  // 相位寄存器1地址
/* Command Control Bits - 控制位定义 */
#define AD9833_B28          (1 << 13)                                                  // B28位：允许连续写入28位频率数据
#define AD9833_HLB          (1 << 12)                                                  // HLB位：控制写入频率寄存器的高/低14位
#define AD9833_FSEL0        (0 << 11)                                                  // 选择频率寄存器0
#define AD9833_FSEL1        (1 << 11)                                                  // 选择频率寄存器1
#define AD9833_PSEL0        (0 << 10)                                                  // 选择相位寄存器0
#define AD9833_PSEL1        (1 << 10)                                                  // 选择相位寄存器1
#define AD9833_PIN_SW       (1 << 9)                                                   // PIN/SW位：控制引脚功能
#define AD9833_RESET        (1 << 8)                                                   // RESET位：复位AD9833
#define AD9833_CLEAR_RESET  (0 << 8)                                                   // 清除RESET位
#define AD9833_SLEEP1       (1 << 7)                                                   // SLEEP1位：DAC省电模式
#define AD9833_SLEEP12      (1 << 6)                                                   // SLEEP12位：内部时钟省电模式
#define AD9833_OPBITEN      (1 << 5)                                                   // OPBITEN位：输出位使能
#define AD9833_SIGN_PIB     (1 << 4)                                                   // SIGN/PIB位：符号位输出控制
#define AD9833_DIV2         (1 << 3)                                                   // DIV2位：除2功能
#define AD9833_MODE         (1 << 1)                                                   // MODE位：输出模式选择
//=============================================================================
// AD9833驱动函数声明 - 增强版本
//=============================================================================

//-----------------------------------------------------------------------------
// 基础驱动函数
//-----------------------------------------------------------------------------
AD9833_StatusTypeDef AD9833_Init(void);                                    // AD9833初始化：配置GPIO和默认参数
void AD9833_WriteData(uint16_t txdata);                                    // 写16位数据：通过SPI接口发送数据到AD9833

//-----------------------------------------------------------------------------
// 配置函数 - 带参数验证
//-----------------------------------------------------------------------------
AD9833_StatusTypeDef AD9833_SetFrequency(uint16_t reg, double fout);       // 设置频率：配置指定寄存器的输出频率(Hz)
AD9833_StatusTypeDef AD9833_SetPhase(uint16_t reg, uint16_t val);         // 设置相位：配置指定寄存器的相位偏移(0-4095)
AD9833_StatusTypeDef AD9833_SetWave(uint32_t WaveMode, uint32_t Freq_SFR, uint32_t Phase_SFR); // 设置波形：选择输出波形类型和寄存器

//-----------------------------------------------------------------------------
// 高级配置函数
//-----------------------------------------------------------------------------
AD9833_StatusTypeDef AD9833_Setup(uint32_t Freq_SFR, double Freq, uint32_t Phase_SFR, uint32_t Phase, uint32_t WaveMode); // 完整配置：一次性设置所有参数
AD9833_StatusTypeDef AD9833_ConfigureFromStruct(const AD9833_ConfigTypeDef* config); // 结构体配置：从配置结构体设置参数

//-----------------------------------------------------------------------------
// 便捷函数
//-----------------------------------------------------------------------------
AD9833_StatusTypeDef AD9833_SetDefaultConfig(void);                        // 设置默认配置：恢复到预设的默认参数
AD9833_StatusTypeDef AD9833_Reset(void);                                   // 复位AD9833：硬件复位芯片到初始状态
void AD9833_GetConfig(AD9833_ConfigTypeDef* config);                       // 获取当前配置：读取当前的配置参数

//-----------------------------------------------------------------------------
// 兼容性函数 - 保持原有接口
//-----------------------------------------------------------------------------
void AD9833_SetFrequency_Legacy(uint16_t reg, double fout);                // 兼容旧版频率设置：无返回值的频率设置
void AD9833_SetPhase_Legacy(uint16_t reg, uint16_t val);                   // 兼容旧版相位设置：无返回值的相位设置
void AD9833_Setup_Legacy(uint32_t Freq_SFR, double Freq, uint32_t Phase_SFR, uint32_t Phase, uint32_t WaveMode); // 兼容旧版配置：无返回值的完整配置

#endif                                                                                 // 结束头文件保护






