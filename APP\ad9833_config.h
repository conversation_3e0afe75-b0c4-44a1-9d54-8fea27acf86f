#ifndef _AD9833_CONFIG_H
#define _AD9833_CONFIG_H

#include "main.h"

//=============================================================================
// AD9833配置参数 - 统一管理
//=============================================================================

//-----------------------------------------------------------------------------
// GPIO配置参数
//-----------------------------------------------------------------------------
#define AD9833_GPIO_PORT        GPIOC          // GPIO端口
#define AD9833_SDATA_PIN        GPIO_PIN_0      // SDATA引脚 - PC0
#define AD9833_SCLK_PIN         GPIO_PIN_1      // SCLK引脚  - PC1  
#define AD9833_FSYNC_PIN        GPIO_PIN_2      // FSYNC引脚 - PC2

//-----------------------------------------------------------------------------
// 时序配置参数
//-----------------------------------------------------------------------------
#define AD9833_DELAY_CYCLES     1               // SPI时序延时周期数
#define AD9833_GPIO_SPEED       GPIO_SPEED_FREQ_HIGH  // GPIO速度配置

//-----------------------------------------------------------------------------
// 频率计算配置参数
//-----------------------------------------------------------------------------
#define AD9833_MCLK_HZ          25000000UL      // 主时钟频率 25MHz
#define AD9833_FREQ_RESOLUTION  268435456UL     // 频率分辨率 (2^28)
#define AD9833_FREQ_SCALE       (AD9833_FREQ_RESOLUTION / AD9833_MCLK_HZ)

//-----------------------------------------------------------------------------
// 默认配置参数
//-----------------------------------------------------------------------------
#define AD9833_DEFAULT_FREQ     1000.0          // 默认频率 1KHz
#define AD9833_DEFAULT_PHASE    0               // 默认相位 0度
#define AD9833_DEFAULT_WAVE     AD9833_OUT_SINUS // 默认波形 正弦波

//-----------------------------------------------------------------------------
// 功能使能配置
//-----------------------------------------------------------------------------
#define AD9833_ENABLE_FREQ_VALIDATION   1       // 使能频率范围验证
#define AD9833_ENABLE_PHASE_VALIDATION  1       // 使能相位范围验证
#define AD9833_ENABLE_ERROR_HANDLING    1       // 使能错误处理

//-----------------------------------------------------------------------------
// 频率和相位范围限制
//-----------------------------------------------------------------------------
#define AD9833_MIN_FREQ_HZ      0.1             // 最小频率 0.1Hz
#define AD9833_MAX_FREQ_HZ      12500000.0      // 最大频率 12.5MHz (MCLK/2)
#define AD9833_MIN_PHASE        0               // 最小相位值
#define AD9833_MAX_PHASE        4095            // 最大相位值 (12位)

//-----------------------------------------------------------------------------
// 错误代码定义
//-----------------------------------------------------------------------------
typedef enum {
    AD9833_OK = 0,                              // 操作成功
    AD9833_ERROR_FREQ_OUT_OF_RANGE,             // 频率超出范围
    AD9833_ERROR_PHASE_OUT_OF_RANGE,            // 相位超出范围
    AD9833_ERROR_INVALID_REGISTER,              // 无效寄存器
    AD9833_ERROR_INVALID_WAVEFORM               // 无效波形类型
} AD9833_StatusTypeDef;

//-----------------------------------------------------------------------------
// 配置结构体定义
//-----------------------------------------------------------------------------
typedef struct {
    uint32_t freq_reg;                          // 频率寄存器选择
    double frequency;                           // 频率值 (Hz)
    uint32_t phase_reg;                         // 相位寄存器选择
    uint16_t phase;                             // 相位值 (0-4095)
    uint32_t waveform;                          // 波形类型
} AD9833_ConfigTypeDef;

#endif // _AD9833_CONFIG_H
