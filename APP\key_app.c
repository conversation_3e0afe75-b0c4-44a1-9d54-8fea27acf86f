#include "key_app.h"
#include "ad9833.h"
#include "gpio.h"
#include "main.h"
#include "stm32f4xx_hal.h"


uint8_t key_val,key_old,key_down,key_up;
double Freq=1000.0;
unsigned char signal_source_flag = 0;
uint32_t voltage_level = 0;  // 电压等级，范围0-33 (0V-3.3V，步进0.1V)
const uint32_t max_level = 33;  // 最大等级 (3.3V/0.1V = 33级)
const uint32_t dac_max_value = 4095;  // DAC最大值12位
uint32_t dac_output_value = 0;  // 当前DAC输出值

// DAC电压控制初始化
void dac_voltage_control_init(void)
{
    voltage_level = 0;
    dac_output_value = 0;

    // 确保DAC已启动
    HAL_DAC_Start(&hdac, 0x00000000U);

    // 设置初始值
    HAL_DAC_SetValue(&hdac, 0x00000000U, 0x00000000U, dac_output_value);

    // 强制触发DAC输出
    HAL_DAC_Start(&hdac, 0x00000000U);
}



uint8_t key_read()
{
	uint8_t temp = 0;

	if(HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0) == GPIO_PIN_SET) temp = 4; //KEY_UP
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_2) == GPIO_PIN_RESET) temp = 3; //KEY2
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3) == GPIO_PIN_RESET) temp = 2; //KEY1
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4) == GPIO_PIN_RESET) temp = 1; //KEY0

	return temp;
}

void key_task()
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = ~key_val & (key_old ^ key_val);
	key_old = key_val;
	

	
	if(key_down == 1)
	{
		switch(signal_source_flag)
		{
			case 0:
				Freq = Freq + 100;
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_SINUS);
			break;
			case 1:
				Freq = Freq + 100;
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_MSB);
			break;
			case 2:
				Freq = Freq + 100;
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_TRIANGLE);
			break;
		}
	}
	if(key_down == 2)
	{
		
	}
	if(key_down == 3)
	{// 增加DAC电压等级，超过最大值时循环到零
            voltage_level = (voltage_level + 1) % (max_level + 1);
            // 计算并设置DAC输出值
            dac_output_value = (voltage_level * dac_max_value) / max_level;
            HAL_DAC_SetValue(&hdac, 0x00000000U, 0x00000000U, dac_output_value);
            // 软件触发DAC转换
            HAL_DAC_Start(&hdac, 0x00000000U);
	}
	if(key_down == 4)
	{
		if(signal_source_flag < 2) signal_source_flag++;
		else signal_source_flag = 0;
		switch(signal_source_flag)
		{
			case 0:
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_SINUS);
			break;
			case 1:
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_MSB);
			break;
			case 2:
				AD9833_Setup(AD9833_REG_FREQ0,Freq,AD9833_REG_PHASE1,1024,AD9833_OUT_TRIANGLE);
			break;
		}
	}
}


