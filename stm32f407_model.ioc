#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_8
ADC1.ContinuousConvMode=DISABLE
ADC1.DMAContinuousRequests=DISABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,master,DMAContinuousRequests,ContinuousConvMode,ExternalTrigConv,ScanConvMode,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversion
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.ScanConvMode=ENABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_SOFTWARE
DAC.IPParameters=DAC_Trigger
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_NORMAL
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.Request1=USART1_RX
Dma.RequestsNb=2
Dma.USART1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.1.Instance=DMA2_Stream2
Dma.USART1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.1.Mode=DMA_NORMAL
Dma.USART1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP2=DMA
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM2
Mcu.IP7=TIM3
Mcu.IP8=TIM4
Mcu.IP9=USART1
Mcu.IPNb=10
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PC1
Mcu.Pin11=PC2
Mcu.Pin12=PA0-WKUP
Mcu.Pin13=PA1
Mcu.Pin14=PA2
Mcu.Pin15=PA4
Mcu.Pin16=PA5
Mcu.Pin17=PB0
Mcu.Pin18=PA9
Mcu.Pin19=PA10
Mcu.Pin2=PE4
Mcu.Pin20=PA13
Mcu.Pin21=PA14
Mcu.Pin22=VP_SYS_VS_Systick
Mcu.Pin23=VP_TIM2_VS_ControllerModeReset
Mcu.Pin24=VP_TIM2_VS_ClockSourceITR
Mcu.Pin25=VP_TIM3_VS_ClockSourceINT
Mcu.Pin26=VP_TIM4_VS_ClockSourceINT
Mcu.Pin3=PC14-OSC32_IN
Mcu.Pin4=PC15-OSC32_OUT
Mcu.Pin5=PF9
Mcu.Pin6=PF10
Mcu.Pin7=PH0-OSC_IN
Mcu.Pin8=PH1-OSC_OUT
Mcu.Pin9=PC0
Mcu.PinsNb=27
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZGTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.111
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_2
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPIO_Input
PA1.GPIOParameters=GPIO_Speed
PA1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA1.Signal=S_TIM2_CH2
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Signal=ADCx_IN2
PA4.Signal=COMP_DAC1_group
PA5.GPIOParameters=GPIO_Speed
PA5.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA5.Signal=S_TIM2_CH1_ETR
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Signal=ADCx_IN8
PC0.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PC0.GPIO_Label=AD9833_SDATA
PC0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC0.Locked=true
PC0.PinState=GPIO_PIN_SET
PC0.Signal=GPIO_Output
PC1.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PC1.GPIO_Label=AD9833_SCLK
PC1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC1.Locked=true
PC1.PinState=GPIO_PIN_SET
PC1.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC2.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PC2.GPIO_Label=AD9833_FSYNC
PC2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC2.Locked=true
PC2.PinState=GPIO_PIN_SET
PC2.Signal=GPIO_Output
PE2.GPIOParameters=GPIO_PuPd
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PF10.Locked=true
PF10.Signal=GPIO_Output
PF9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PF9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PF9.GPIO_PuPd=GPIO_NOPULL
PF9.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PF9.Locked=true
PF9.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=stm32f407_model.ioc
ProjectManager.ProjectName=stm32f407_model
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_DAC_Init-DAC-false-HAL-true,8-MX_TIM2_Init-TIM2-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN2.0=ADC1_IN2,IN2
SH.ADCx_IN2.ConfNb=1
SH.ADCx_IN8.0=ADC1_IN8,IN8
SH.ADCx_IN8.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_ETR,ClockSourceETR_Mode2
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Input_Capture2_from_TI2
SH.S_TIM2_CH2.ConfNb=1
TIM2.Channel-Input_Capture2_from_TI2=TIM_CHANNEL_2
TIM2.IPParameters=Channel-Input_Capture2_from_TI2
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM3.Period=4200-1
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM4.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM4.IPParameters=Period,Prescaler,AutoReloadPreload,TIM_MasterOutputTrigger
TIM4.Period=9999
TIM4.Prescaler=8399
TIM4.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceITR.Mode=TriggerSource_ITR2
VP_TIM2_VS_ClockSourceITR.Signal=TIM2_VS_ClockSourceITR
VP_TIM2_VS_ControllerModeReset.Mode=Reset Mode
VP_TIM2_VS_ControllerModeReset.Signal=TIM2_VS_ControllerModeReset
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
