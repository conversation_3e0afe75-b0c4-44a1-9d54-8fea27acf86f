--cpu=Cortex-M4.fp.sp
"stm32f407_model\startup_stm32f407xx.o"
"stm32f407_model\main.o"
"stm32f407_model\gpio.o"
"stm32f407_model\adc.o"
"stm32f407_model\dac.o"
"stm32f407_model\dma.o"
"stm32f407_model\tim.o"
"stm32f407_model\usart.o"
"stm32f407_model\stm32f4xx_it.o"
"stm32f407_model\stm32f4xx_hal_msp.o"
"stm32f407_model\stm32f4xx_ll_fsmc.o"
"stm32f407_model\stm32f4xx_hal_sram.o"
"stm32f407_model\stm32f4xx_hal_adc.o"
"stm32f407_model\stm32f4xx_hal_adc_ex.o"
"stm32f407_model\stm32f4xx_ll_adc.o"
"stm32f407_model\stm32f4xx_hal_rcc.o"
"stm32f407_model\stm32f4xx_hal_rcc_ex.o"
"stm32f407_model\stm32f4xx_hal_flash.o"
"stm32f407_model\stm32f4xx_hal_flash_ex.o"
"stm32f407_model\stm32f4xx_hal_flash_ramfunc.o"
"stm32f407_model\stm32f4xx_hal_gpio.o"
"stm32f407_model\stm32f4xx_hal_dma_ex.o"
"stm32f407_model\stm32f4xx_hal_dma.o"
"stm32f407_model\stm32f4xx_hal_pwr.o"
"stm32f407_model\stm32f4xx_hal_pwr_ex.o"
"stm32f407_model\stm32f4xx_hal_cortex.o"
"stm32f407_model\stm32f4xx_hal.o"
"stm32f407_model\stm32f4xx_hal_exti.o"
"stm32f407_model\stm32f4xx_hal_dac.o"
"stm32f407_model\stm32f4xx_hal_dac_ex.o"
"stm32f407_model\stm32f4xx_hal_tim.o"
"stm32f407_model\stm32f4xx_hal_tim_ex.o"
"stm32f407_model\stm32f4xx_hal_uart.o"
"stm32f407_model\system_stm32f4xx.o"
"stm32f407_model\lcd.o"
"stm32f407_model\scheduler.o"
"stm32f407_model\led_app.o"
"stm32f407_model\uart_app.o"
"stm32f407_model\adc_app.o"
"stm32f407_model\waveform_analyzer_app.o"
"stm32f407_model\ad9833.o"
"stm32f407_model\key_app.o"
"stm32f407_model\basicmathfunctions.o"
"stm32f407_model\basicmathfunctionsf16.o"
"stm32f407_model\bayesfunctions.o"
"stm32f407_model\bayesfunctionsf16.o"
"stm32f407_model\commontables.o"
"stm32f407_model\commontablesf16.o"
"stm32f407_model\complexmathfunctions.o"
"stm32f407_model\complexmathfunctionsf16.o"
"stm32f407_model\controllerfunctions.o"
"stm32f407_model\distancefunctions.o"
"stm32f407_model\distancefunctionsf16.o"
"stm32f407_model\fastmathfunctions.o"
"stm32f407_model\fastmathfunctionsf16.o"
"stm32f407_model\filteringfunctions.o"
"stm32f407_model\filteringfunctionsf16.o"
"stm32f407_model\interpolationfunctions.o"
"stm32f407_model\interpolationfunctionsf16.o"
"stm32f407_model\matrixfunctions.o"
"stm32f407_model\matrixfunctionsf16.o"
"stm32f407_model\quaternionmathfunctions.o"
"stm32f407_model\svmfunctions.o"
"stm32f407_model\svmfunctionsf16.o"
"stm32f407_model\statisticsfunctions.o"
"stm32f407_model\statisticsfunctionsf16.o"
"stm32f407_model\supportfunctions.o"
"stm32f407_model\supportfunctionsf16.o"
"stm32f407_model\transformfunctions.o"
"stm32f407_model\transformfunctionsf16.o"
"stm32f407_model\windowfunctions.o"
--library_type=microlib --strict --scatter "stm32f407_model\stm32f407_model.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "stm32f407_model.map" -o stm32f407_model\stm32f407_model.axf