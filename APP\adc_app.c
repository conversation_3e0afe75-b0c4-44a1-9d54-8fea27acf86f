#include "main.h"
#include "adc_app.h"
#include "adc.h"
#include "math.h"
#include "string.h"
#include "usart.h"
#include "tim.h"


#define BUFFER_SIZE 2048

extern DMA_HandleTypeDef hdma_adc1;


uint32_t AD1_Buffer[BUFFER_SIZE / 2];
uint32_t AD2_Buffer[BUFFER_SIZE / 2];
__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;

void adc_tim_dma_init(void)
{
		HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    UNUSED(hadc);
    if(hadc == &hadc1)
    {
        HAL_ADC_Stop_DMA(hadc);
        AdcConvEnd = 1;
    }
}



float dac_app_get_adc_sampling_interval_us(void)
{
	return 50.0f;
}

void adc_task(void)
{

    if(AdcConvEnd)
    {
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            AD1_Buffer[i] = adc_val_buffer[i * 2 ];
        }
				for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            AD2_Buffer[i] = adc_val_buffer[i*2+1];
        }		
//				WaveformInfo waveform_info1 = Get_Waveform_Info(AD1_Buffer);
//        WaveformInfo waveform_info2 = Get_Waveform_Info(AD2_Buffer);
//				my_printf(&huart1, "AD1_Value:\r\n");
//				print_harmonic_data(&waveform_info1);
//				my_printf(&huart1, "AD2_Value:\r\n");
//				print_harmonic_data(&waveform_info2);
////				display_harmonic_results(&waveform_info1);
        
        
        memset(AD1_Buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));
				memset(AD2_Buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
        AdcConvEnd = 0;
    }
}

void print_harmonic_data(const WaveformInfo *info)
{
    if (info->frequency > 0.0f) {
        my_printf(&huart1, "{fundamental}freq:%.2f,amp:%.4f,phase:%.2f\r\n",
                  info->frequency, info->fundamental_fft_amp, info->phase);
        my_printf(&huart1, "{harmonic3}freq:%.2f,amp:%.4f,phase:%.2f,ratio:%.2f\r\n",
                  info->third_harmonic.frequency, info->third_harmonic.amplitude,
                  info->third_harmonic.phase, info->third_harmonic.relative_amp * 100.0f);
        my_printf(&huart1, "{harmonic5}freq:%.2f,amp:%.4f,phase:%.2f,ratio:%.2f\r\n",
                  info->fifth_harmonic.frequency, info->fifth_harmonic.amplitude,
                  info->fifth_harmonic.phase, info->fifth_harmonic.relative_amp * 100.0f);
    } else {
        my_printf(&huart1, "{status}No valid signal detected\r\n");
    }
}


void display_harmonic_results(WaveformInfo* info)
{
    lcd_fill(0, 40, 480, 150, WHITE); // 清除显示区域
    if (info->frequency > 0.0f) {
        // 显示基波信息
        lcd_show_string(10, 50, 200, 16, 16, "Fundamental:", BLACK);
        lcd_show_float(120, 50, info->frequency, 4, 1, 16, BLUE);
        lcd_show_string(200, 50, 50, 16, 16, "Hz", BLACK);
        lcd_show_float(250, 50, info->fundamental_fft_amp, 1, 4, 16, BLUE);
        lcd_show_string(350, 50, 20, 16, 16, "V", BLACK);

        // 显示三次谐波信息
        lcd_show_string(10, 80, 200, 16, 16, "3rd Harmonic:", BLACK);
        lcd_show_float(120, 80, info->third_harmonic.frequency, 4, 1, 16, GREEN);
        lcd_show_string(200, 80, 50, 16, 16, "Hz", BLACK);
        lcd_show_float(250, 80, info->third_harmonic.amplitude, 1, 4, 16, GREEN);
        lcd_show_string(350, 80, 20, 16, 16, "V", BLACK);
        lcd_show_float(380, 80, info->third_harmonic.relative_amp * 100.0f, 2, 1, 16, GREEN);
        lcd_show_string(450, 80, 20, 16, 16, "%", BLACK);

        // 显示五次谐波信息
        lcd_show_string(10, 110, 200, 16, 16, "5th Harmonic:", BLACK);
        lcd_show_float(120, 110, info->fifth_harmonic.frequency, 4, 1, 16, RED);
        lcd_show_string(200, 110, 50, 16, 16, "Hz", BLACK);
        lcd_show_float(250, 110, info->fifth_harmonic.amplitude, 1, 4, 16, RED);
        lcd_show_string(350, 110, 20, 16, 16, "V", BLACK);
        lcd_show_float(380, 110, info->fifth_harmonic.relative_amp * 100.0f, 2, 1, 16, RED);
        lcd_show_string(450, 110, 20, 16, 16, "%", BLACK);

        // 显示信号状态
        lcd_show_string(10, 140, 100, 16, 16, "Signal OK", GREEN);
    } else {
        // 显示无信号状态和可能的原因
        lcd_show_string(10, 70, 300, 16, 16, "No valid signal detected", RED);
        lcd_show_string(10, 100, 350, 16, 16, "Check: Signal amplitude > 0.1V", GRAY);
        lcd_show_string(10, 120, 350, 16, 16, "or 50Hz power interference", GRAY);
    }
}


